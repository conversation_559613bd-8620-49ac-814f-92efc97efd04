import React, { useEffect, useState } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Alert,
  Snackbar,
  LinearProgress,
  Typography,
} from '@mui/material';
import { AppProvider, useApp } from './contexts/AppContext';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import ActionBar from './components/ActionBar';
import Breadcrumbs from './components/Breadcrumbs';
import FileGrid from './components/FileGrid';
import { fileApi } from './services/api';
import { Item, BreadcrumbItem } from './types';
import { validateFileName } from './utils/helpers';

const theme = createTheme({
  palette: {
    primary: {
      main: '#0061FF',
      light: '#4285FF',
      dark: '#0047CC',
    },
    secondary: {
      main: '#7C3AED',
      light: '#A855F7',
      dark: '#5B21B6',
    },
    background: {
      default: '#FAFBFC',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#1E1E1E',
      secondary: '#637381',
    },
    grey: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
    },
  },
  typography: {
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    h4: {
      fontWeight: 600,
      fontSize: '1.75rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    body1: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.75rem',
      lineHeight: 1.4,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #E5E7EB',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            borderColor: '#D1D5DB',
          },
          transition: 'all 0.2s ease-in-out',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 8,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 8px rgba(0,97,255,0.3)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderBottom: '1px solid #E5E7EB',
        },
      },
    },
  },
});

const AppContent: React.FC = () => {
  const { state, dispatch } = useApp();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [newFolderName, setNewFolderName] = useState('');
  const [newItemName, setNewItemName] = useState('');
  const [itemToRename, setItemToRename] = useState<Item | null>(null);
  const [itemToDelete, setItemToDelete] = useState<Item | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load folder content
  const loadFolderContent = async (folderId: string | null) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await fileApi.browseFolder(folderId || undefined);
      if (response.code === 200 && response.data) {
        const allItems: Item[] = [...response.data.folders, ...response.data.files];
        dispatch({ type: 'SET_ITEMS', payload: allItems });
        dispatch({ type: 'SET_CURRENT_FOLDER', payload: folderId });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load folder content' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Build breadcrumbs (simplified - in real app, you'd track the full path)
  const buildBreadcrumbs = (folderId: string | null): BreadcrumbItem[] => {
    if (!folderId) {
      return [{ id: null, name: 'Home' }];
    }
    // In a real app, you'd build the full path by traversing parent folders
    return [
      { id: null, name: 'Home' },
      { id: folderId, name: 'Current Folder' },
    ];
  };

  // Navigate to folder
  const handleNavigate = (folderId: string | null) => {
    const breadcrumbs = buildBreadcrumbs(folderId);
    dispatch({ type: 'SET_BREADCRUMBS', payload: breadcrumbs });
    dispatch({ type: 'CLEAR_SEARCH' });
    loadFolderContent(folderId);
  };

  // Handle item click
  const handleItemClick = (item: Item) => {
    if (item.type === 'folder') {
      handleNavigate(item.id);
    }
    // For files, you could open a preview dialog
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFiles) return;

    const files = Array.from(selectedFiles);

    for (const file of files) {
      dispatch({
        type: 'ADD_UPLOAD',
        payload: { fileName: file.name, progress: 0, status: 'uploading' },
      });

      try {
        await fileApi.uploadFile(
          file,
          state.currentFolderId || undefined,
          (progress) => {
            dispatch({
              type: 'UPDATE_UPLOAD',
              payload: { fileName: file.name, progress, status: 'uploading' },
            });
          }
        );

        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 100, status: 'completed' },
        });

        // Remove upload progress after 2 seconds
        setTimeout(() => {
          dispatch({ type: 'REMOVE_UPLOAD', payload: file.name });
        }, 2000);

      } catch (error: any) {
        dispatch({
          type: 'UPDATE_UPLOAD',
          payload: { fileName: file.name, progress: 0, status: 'error' },
        });
        setSnackbar({
          open: true,
          message: `Failed to upload ${file.name}: ${error.message}`,
          severity: 'error',
        });
      }
    }

    setUploadDialogOpen(false);
    setSelectedFiles(null);

    // Reload current folder
    loadFolderContent(state.currentFolderId);
  };

  // Handle create folder
  const handleCreateFolder = async () => {
    if (!validateFileName(newFolderName)) {
      setSnackbar({
        open: true,
        message: 'Invalid folder name',
        severity: 'error',
      });
      return;
    }

    try {
      await fileApi.createFolder(newFolderName, state.currentFolderId || undefined);
      setSnackbar({
        open: true,
        message: 'Folder created successfully',
        severity: 'success',
      });
      setCreateFolderDialogOpen(false);
      setNewFolderName('');
      loadFolderContent(state.currentFolderId);
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: error.message || 'Failed to create folder',
        severity: 'error',
      });
    }
  };

  // Handle rename item
  const handleRenameItem = async () => {
    if (!itemToRename || !validateFileName(newItemName)) {
      setSnackbar({
        open: true,
        message: 'Invalid name',
        severity: 'error',
      });
      return;
    }

    try {
      console.log('Renaming item:', itemToRename.id, 'to:', newItemName);
      const response = await fileApi.renameItem(itemToRename.id, newItemName);
      console.log('Rename response:', response);

      setSnackbar({
        open: true,
        message: 'Item renamed successfully',
        severity: 'success',
      });
      setRenameDialogOpen(false);
      setItemToRename(null);
      setNewItemName('');

      // Force refresh with a small delay
      setTimeout(() => {
        console.log('Refreshing folder content after rename');
        loadFolderContent(state.currentFolderId);
      }, 500);
    } catch (error: any) {
      console.error('Rename error:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to rename item',
        severity: 'error',
      });
    }
  };

  // Handle delete item
  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      console.log('Deleting item:', itemToDelete.id);
      const response = await fileApi.deleteItem(itemToDelete.id);
      console.log('Delete response:', response);

      setSnackbar({
        open: true,
        message: 'Item deleted successfully',
        severity: 'success',
      });
      setDeleteDialogOpen(false);
      setItemToDelete(null);

      // Force refresh with a small delay
      setTimeout(() => {
        console.log('Refreshing folder content after delete');
        loadFolderContent(state.currentFolderId);
      }, 500);
    } catch (error: any) {
      console.error('Delete error:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to delete item',
        severity: 'error',
      });
    }
  };

  // Initialize app
  useEffect(() => {
    loadFolderContent(null);
  }, []);

  const displayItems = state.searchQuery ? state.searchResults : state.items;

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: '#FAFBFC',
      }}
    >
      <Header
        onUploadClick={() => setUploadDialogOpen(true)}
        onHomeClick={() => handleNavigate(null)}
      />

      <Box sx={{ display: 'flex', flex: 1 }}>
        <Sidebar
          onNavigate={handleNavigate}
          currentFolderId={state.currentFolderId}
        />

        <Box
          sx={{
            flex: 1,
            ml: '240px', // Sidebar width
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <ActionBar
            onUploadClick={() => setUploadDialogOpen(true)}
            onCreateFolderClick={() => setCreateFolderDialogOpen(true)}
          />

          <Box sx={{ px: 3, py: 2 }}>
            <Breadcrumbs onNavigate={handleNavigate} />
          </Box>

          {state.loading && (
            <Box sx={{ px: 3, mb: 2 }}>
              <LinearProgress
                sx={{
                  borderRadius: 2,
                  height: 4,
                  backgroundColor: '#E5E7EB',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#0061FF',
                  }
                }}
              />
            </Box>
          )}

          {state.error && (
            <Box sx={{ px: 3, mb: 2 }}>
              <Alert
                severity="error"
                sx={{
                  borderRadius: 2,
                  border: '1px solid #FEE2E2',
                  backgroundColor: '#FEF2F2',
                }}
              >
                {state.error}
              </Alert>
            </Box>
          )}

          <Box
            sx={{
              flex: 1,
              px: 3,
              pb: 3,
            }}
          >
            <FileGrid
              items={displayItems}
              onItemClick={handleItemClick}
              onItemRename={(item) => {
                setItemToRename(item);
                setNewItemName(item.name);
                setRenameDialogOpen(true);
              }}
              onItemDelete={(item) => {
                setItemToDelete(item);
                setDeleteDialogOpen(true);
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* Upload Progress */}
      {state.uploads.length > 0 && (
        <Box sx={{ position: 'fixed', bottom: 20, right: 20, width: 300, zIndex: 1300 }}>
          {state.uploads.map((upload) => (
            <Box
              key={upload.fileName}
              sx={{
                mb: 1,
                p: 2,
                backgroundColor: '#FFFFFF',
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                border: '1px solid #E5E7EB',
              }}
            >
              <Typography variant="body2" noWrap sx={{ fontSize: '0.875rem', mb: 1 }}>
                {upload.fileName}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={upload.progress}
                color={upload.status === 'error' ? 'error' : 'primary'}
                sx={{
                  height: 4,
                  borderRadius: 2,
                  backgroundColor: '#E5E7EB',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: upload.status === 'error' ? '#EF4444' : '#0061FF',
                  }
                }}
              />
            </Box>
          ))}
        </Box>
      )}

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Files</DialogTitle>
        <DialogContent>
          <input
            type="file"
            multiple
            onChange={(e) => setSelectedFiles(e.target.files)}
            style={{ width: '100%', padding: '10px' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleFileUpload} disabled={!selectedFiles} variant="contained">
            Upload
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Folder Dialog */}
      <Dialog open={createFolderDialogOpen} onClose={() => setCreateFolderDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Folder</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Folder Name"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateFolderDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateFolder} variant="contained">
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Rename {itemToRename?.type === 'folder' ? 'Folder' : 'File'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Name"
            fullWidth
            variant="outlined"
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRenameDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleRenameItem} variant="contained">
            Rename
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Delete {itemToDelete?.type === 'folder' ? 'Folder' : 'File'}</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{itemToDelete?.name}"?
            {itemToDelete?.type === 'folder' && ' This will also delete all contents inside this folder.'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteItem} variant="contained" color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
